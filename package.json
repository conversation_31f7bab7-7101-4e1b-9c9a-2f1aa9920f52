{"name": "givf-web-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "dev:prod": "NODE_ENV=production next dev --turbopack", "build": "prisma generate && next build", "start": "next start", "lint": "next lint", "format:check": "prettier --check \"**/*.{js,jsx,ts,tsx,json,css,md}\"", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,css,md}\"", "test": "jest", "test:components": "jest --testPathPatterns=src/components", "test:client": "jest --selectProjects client", "test:server": "jest --selectProjects server", "prepare": "is-ci || husky", "prisma:seed:dev": "dotenv -e .env.local -- tsx prisma/seed.ts", "prisma:seed:prod": "dotenv -e .env.prod -- tsx prisma/seed.ts", "prisma:generate": "prisma generate", "prisma:migrate:dev": "dotenv -e .env.local -- prisma migrate dev", "prisma:migrate:deploy": "dotenv -e .env.prod -- prisma migrate deploy", "prisma:migrate:reset:dev": "dotenv -e .env.local -- prisma migrate reset", "prisma:migrate:reset:prod": "dotenv -e .env.prod -- prisma migrate reset", "prisma:dev": "npm run prisma:generate && npm run prisma:migrate:dev", "prisma:prod": "npm run prisma:generate && npm run prisma:migrate:deploy", "prisma:reset:dev": "npm run prisma:migrate:reset:dev && npm run prisma:generate", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "prisma": {"schema": "prisma/schema"}, "dependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@commitlint/types": "^19.8.1", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@hookform/resolvers": "^5.1.1", "@prisma/client": "^6.10.1", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@supabase-cache-helpers/postgrest-react-query": "^1.13.4", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.50.0", "@tanstack/react-query": "^5.80.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "graphql": "^16.11.0", "lucide-react": "^0.522.0", "next": "15.3.3", "pnpm": "^10.12.1", "prettier": "^3.5.3", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.60.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@chromatic-com/storybook": "^4.0.1", "@storybook/addon-docs": "^9.0.14", "@storybook/addon-onboarding": "^9.0.14", "@storybook/nextjs": "^9.0.14", "@tailwindcss/postcss": "^4.1.10", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "@types/node": "^20.19.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "dotenv": "^16.5.0", "dotenv-cli": "^8.0.0", "eslint": "^9.29.0", "eslint-config-next": "15.3.3", "eslint-plugin-storybook": "^9.0.14", "husky": "^9.1.7", "is-ci": "^4.1.0", "jest": "^30.0.0", "jest-environment-jsdom": "^30.0.2", "postcss": "^8.5.6", "prisma": "^6.10.1", "react": "^19.0.0", "react-dom": "^19.0.0", "storybook": "^9.0.14", "tailwindcss": "^4.1.10", "ts-jest": "^29.4.0", "tsx": "^4.20.3", "tw-animate-css": "^1.3.4", "whatwg-fetch": "^3.6.20", "zod": "^3.25.74"}}