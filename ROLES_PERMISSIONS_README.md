# Roles & Permissions System

## Overview

The GIVF application now includes a comprehensive roles and permissions system that allows administrators to create custom roles and assign granular permissions for different resources in the system.

## Features

### ✅ **Admin Dashboard**
- Modern, clean interface built with shadcn/ui components
- Overview of system statistics and quick actions
- Responsive design for desktop and mobile

### ✅ **Roles Management**
- Create new roles with custom names and descriptions
- Edit existing roles and their permissions
- Delete roles (with safety checks for assigned users)
- View all roles in a comprehensive table

### ✅ **Permissions Matrix**
- CRUD permissions (Create, Read, Update, Delete) for each resource
- Visual matrix interface for easy permission assignment
- Bulk selection options (select all permissions for a resource)
- Clear descriptions for each resource and permission type

### ✅ **Protected Resources**
- **Profiles**: User profile information and settings
- **IVF Scores**: IVF scoring data and assessments
- **Roles**: System roles and role management
- **Permissions**: Permission definitions and assignments
- **Users**: User accounts and authentication
- **Guest Sessions**: Guest user sessions and temporary data
- **OTPs**: One-time passwords and verification codes

## File Structure

```
src/
├── app/
│   ├── admin/
│   │   ├── layout.tsx                 # Admin layout with navigation
│   │   ├── page.tsx                   # Admin dashboard
│   │   └── roles-permissions/
│   │       └── page.tsx               # Roles & permissions page
│   └── api/v1/admin/
│       └── roles/
│           └── route.ts               # Roles API endpoints
├── components/admin/roles/
│   ├── RolesTable.tsx                 # Roles data table
│   ├── CreateRoleDialog.tsx           # Create role modal
│   ├── EditRoleDialog.tsx             # Edit role modal
│   ├── DeleteRoleDialog.tsx           # Delete confirmation modal
│   └── PermissionsMatrix.tsx          # Permissions assignment matrix
├── hooks/
│   └── useRolesPermissions.ts         # Custom hook for roles management
└── types/
    └── roles-permissions.ts           # TypeScript types and interfaces
```

## API Endpoints

### Roles Management
- `GET /api/v1/admin/roles` - Get all roles with permissions
- `POST /api/v1/admin/roles` - Create a new role
- `PUT /api/v1/admin/roles` - Update an existing role
- `DELETE /api/v1/admin/roles?id={roleId}` - Delete a role

## Usage

### Accessing the Admin Panel
1. Navigate to `/admin` to access the admin dashboard
2. Click on "Roles & Permissions" to manage roles

### Creating a New Role
1. Click the "Create Role" button
2. Enter role name and description
3. Use the permissions matrix to assign CRUD permissions for each resource
4. Click "Create Role" to save

### Editing a Role
1. Click the edit icon next to any role in the table
2. Modify the role details and permissions as needed
3. Click "Update Role" to save changes

### Deleting a Role
1. Click the delete icon next to any role in the table
2. Review the role details and permissions to be deleted
3. Confirm deletion (note: roles assigned to users cannot be deleted)

## Permission Types

### CRUD Operations
- **Create**: Can create new records of the resource
- **Read**: Can view and list records of the resource
- **Update**: Can modify existing records of the resource
- **Delete**: Can remove records of the resource

### Resource Descriptions
- **Profiles**: User profile information, settings, and personal data
- **IVF Scores**: IVF assessment data, scoring results, and related analytics
- **Roles**: System roles, role definitions, and role management
- **Permissions**: Permission definitions, assignments, and access control
- **Users**: User accounts, authentication, and user management
- **Guest Sessions**: Temporary guest user sessions and associated data
- **OTPs**: One-time passwords, verification codes, and authentication tokens

## Security Features

### Role Validation
- Role names must be unique
- Role names are required and cannot be empty
- Descriptions are optional but recommended

### Permission Safety
- Roles assigned to users cannot be deleted
- Permission changes are applied immediately
- All API endpoints require authentication

### Data Integrity
- Transactional operations ensure data consistency
- Cascade deletion of permissions when roles are deleted
- Validation of permission assignments

## Technical Implementation

### Database Schema
The system uses the existing Prisma schema with:
- `roles` table for role definitions
- `permissions` table for permission assignments
- `user_roles` table for user-role associations

### Frontend Architecture
- **React Components**: Modern functional components with hooks
- **State Management**: Custom hooks for API interactions
- **UI Components**: shadcn/ui for consistent design
- **Form Handling**: Controlled components with validation
- **TypeScript**: Full type safety throughout the application

### API Design
- RESTful API endpoints with proper HTTP methods
- Comprehensive error handling and validation
- Consistent response formats
- Authentication middleware integration

## Future Enhancements

### Planned Features
- [ ] Role templates for common use cases
- [ ] Permission inheritance and role hierarchies
- [ ] Audit logging for role and permission changes
- [ ] Bulk user role assignments
- [ ] Advanced permission conditions and rules
- [ ] Role-based UI component visibility
- [ ] Permission caching for improved performance

### Integration Points
- [ ] User management interface
- [ ] Permission checking middleware
- [ ] Role-based route protection
- [ ] Dynamic menu generation based on permissions

## Development Notes

### Adding New Resources
1. Add the resource name to `AVAILABLE_RESOURCES` in `types/roles-permissions.ts`
2. Update the resource descriptions in `PermissionsMatrix.tsx`
3. Implement permission checking in the relevant API endpoints

### Customizing Permissions
The permission system is designed to be flexible. You can:
- Add new permission types beyond CRUD
- Implement conditional permissions with the `condition` field
- Create resource-specific permission logic

### Testing
- Test role creation, editing, and deletion
- Verify permission matrix functionality
- Test API endpoints with various permission combinations
- Ensure proper error handling for edge cases

## Support

For questions or issues with the roles and permissions system, please refer to the codebase documentation or create an issue in the project repository.
