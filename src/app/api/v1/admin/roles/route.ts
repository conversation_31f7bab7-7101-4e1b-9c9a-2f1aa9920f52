import { NextRequest } from "next/server";
import { authenticate } from "@/utils/api/authenticate";
import { apiResponse } from "@/utils/api/apiResponse";
import { PrismaClient } from "@/generated/prisma";
import { z } from "zod";

const prisma = new PrismaClient();

// Validation schemas
const createRoleSchema = z.object({
  name: z.string().min(1, "Role name is required").max(100, "Role name too long"),
  description: z.string().max(500, "Description too long").optional(),
  permissions: z.array(z.object({
    resource: z.string().min(1, "Resource is required"),
    can_create: z.boolean().default(false),
    can_read: z.boolean().default(false),
    can_update: z.boolean().default(false),
    can_delete: z.boolean().default(false),
    condition: z.string().optional(),
  })).optional().default([]),
});

const updateRoleSchema = createRoleSchema.partial();

/**
 * GET /api/v1/admin/roles
 * Get all roles with their permissions
 */
export async function GET(req: NextRequest) {
  try {
    const { user, error } = await authenticate(req);
    if (error) return error;

    // Check if user has admin permissions
    // TODO: Implement proper permission checking
    
    const roles = await prisma.roles.findMany({
      include: {
        permissions: true,
      },
      orderBy: {
        created_at: 'desc',
      },
    });

    console.log(roles)

    return apiResponse(200, undefined, { roles });
  } catch (error) {
    console.error("GET roles error:", error);
    return apiResponse(500, "Internal server error");
  }
}

/**
 * POST /api/v1/admin/roles
 * Create a new role with permissions
 */
export async function POST(req: NextRequest) {
  try {
    const { user, error } = await authenticate(req);
    if (error) return error;

    // Check if user has admin permissions
    // TODO: Implement proper permission checking

    const body = await req.json();
    const result = createRoleSchema.safeParse(body);

    if (!result.success) {
      const errorMessage = result.error.issues
        .map((issue) => `${issue.path.join(".")}: ${issue.message}`)
        .join(", ");
      return apiResponse(400, errorMessage);
    }

    const { name, description, permissions } = result.data;

    // Check if role name already exists
    const existingRole = await prisma.roles.findFirst({
      where: { name },
    });

    if (existingRole) {
      return apiResponse(409, "Role with this name already exists");
    }

    // Create role with permissions in a transaction
    const role = await prisma.$transaction(async (tx) => {
      // Create the role
      const newRole = await tx.roles.create({
        data: {
          name,
          description,
        },
      });

      // Create permissions if provided
      if (permissions.length > 0) {
        await tx.permissions.createMany({
          data: permissions.map((permission) => ({
            role_id: newRole.id,
            resource: permission.resource,
            can_create: permission.can_create,
            can_read: permission.can_read,
            can_update: permission.can_update,
            can_delete: permission.can_delete,
            condition: permission.condition,
          })),
        });
      }

      // Return role with permissions
      return await tx.roles.findUnique({
        where: { id: newRole.id },
        include: { permissions: true },
      });
    });

    return apiResponse(201, "Role created successfully", { role });
  } catch (error) {
    console.error("POST role error:", error);
    return apiResponse(500, "Internal server error");
  }
}

/**
 * PUT /api/v1/admin/roles
 * Update an existing role
 */
export async function PUT(req: NextRequest) {
  try {
    const { user, error } = await authenticate(req);
    if (error) return error;

    // Check if user has admin permissions
    // TODO: Implement proper permission checking

    const body = await req.json();
    const { id, ...updateData } = body;

    if (!id) {
      return apiResponse(400, "Role ID is required");
    }

    const result = updateRoleSchema.safeParse(updateData);

    if (!result.success) {
      const errorMessage = result.error.issues
        .map((issue) => `${issue.path.join(".")}: ${issue.message}`)
        .join(", ");
      return apiResponse(400, errorMessage);
    }

    const { name, description, permissions } = result.data;

    // Check if role exists
    const existingRole = await prisma.roles.findUnique({
      where: { id: BigInt(id) },
    });

    if (!existingRole) {
      return apiResponse(404, "Role not found");
    }

    // Check if new name conflicts with existing role
    if (name && name !== existingRole.name) {
      const nameConflict = await prisma.roles.findFirst({
        where: { 
          name,
          id: { not: BigInt(id) },
        },
      });

      if (nameConflict) {
        return apiResponse(409, "Role with this name already exists");
      }
    }

    // Update role with permissions in a transaction
    const role = await prisma.$transaction(async (tx) => {
      // Update the role
      const updatedRole = await tx.roles.update({
        where: { id: BigInt(id) },
        data: {
          ...(name && { name }),
          ...(description !== undefined && { description }),
        },
      });

      // Update permissions if provided
      if (permissions) {
        // Delete existing permissions
        await tx.permissions.deleteMany({
          where: { role_id: BigInt(id) },
        });

        // Create new permissions
        if (permissions.length > 0) {
          await tx.permissions.createMany({
            data: permissions.map((permission) => ({
              role_id: BigInt(id),
              resource: permission.resource,
              can_create: permission.can_create,
              can_read: permission.can_read,
              can_update: permission.can_update,
              can_delete: permission.can_delete,
              condition: permission.condition,
            })),
          });
        }
      }

      // Return updated role with permissions
      return await tx.roles.findUnique({
        where: { id: BigInt(id) },
        include: { permissions: true },
      });
    });

    return apiResponse(200, "Role updated successfully", { role });
  } catch (error) {
    console.error("PUT role error:", error);
    return apiResponse(500, "Internal server error");
  }
}

/**
 * DELETE /api/v1/admin/roles
 * Delete a role and its permissions
 */
export async function DELETE(req: NextRequest) {
  try {
    const { user, error } = await authenticate(req);
    if (error) return error;

    // Check if user has admin permissions
    // TODO: Implement proper permission checking

    const url = new URL(req.url);
    const id = url.searchParams.get("id");

    if (!id) {
      return apiResponse(400, "Role ID is required");
    }

    // Check if role exists
    const existingRole = await prisma.roles.findUnique({
      where: { id: BigInt(id) },
    });

    if (!existingRole) {
      return apiResponse(404, "Role not found");
    }

    // Check if role is being used by any users
    const usersWithRole = await prisma.user_roles.findMany({
      where: { role_id: BigInt(id) },
    });

    if (usersWithRole.length > 0) {
      return apiResponse(400, "Cannot delete role that is assigned to users");
    }

    // Delete role and its permissions (cascade delete)
    await prisma.roles.delete({
      where: { id: BigInt(id) },
    });

    return apiResponse(200, "Role deleted successfully");
  } catch (error) {
    console.error("DELETE role error:", error);
    return apiResponse(500, "Internal server error");
  }
}
