"use client";

import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ShadcnUI/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ShadcnUI/card";
import { Badge } from "@/components/ShadcnUI/badge";
import { Plus, Edit, Trash2, Shield, Users } from "lucide-react";
import { RolesTable } from "@/components/admin/roles/RolesTable";
import { CreateRoleDialog } from "@/components/admin/roles/CreateRoleDialog";
import { EditRoleDialog } from "@/components/admin/roles/EditRoleDialog";
import { DeleteRoleDialog } from "@/components/admin/roles/DeleteRoleDialog";
import { useRolesPermissions } from "@/hooks/useRolesPermissions";
import type { RoleWithPermissions } from "@/types/roles-permissions";

export default function RolesPermissionsPage() {
  const {
    roles,
    loading,
    error,
    createRole,
    updateRole,
    deleteRole,
    refreshRoles,
  } = useRolesPermissions();

  const [selectedRole, setSelectedRole] = useState<RoleWithPermissions | null>(null);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  useEffect(() => {
    refreshRoles();
  }, [refreshRoles]);

  const handleCreateRole = async (roleData: any) => {
    try {
      await createRole(roleData);
      setIsCreateDialogOpen(false);
    } catch (error) {
      console.error("Failed to create role:", error);
    }
  };

  const handleEditRole = (role: RoleWithPermissions) => {
    setSelectedRole(role);
    setIsEditDialogOpen(true);
  };

  const handleUpdateRole = async (roleData: any) => {
    if (!selectedRole) return;
    
    try {
      await updateRole(selectedRole.id.toString(), roleData);
      setIsEditDialogOpen(false);
      setSelectedRole(null);
    } catch (error) {
      console.error("Failed to update role:", error);
    }
  };

  const handleDeleteRole = (role: RoleWithPermissions) => {
    setSelectedRole(role);
    setIsDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (!selectedRole) return;
    
    try {
      await deleteRole(selectedRole.id.toString());
      setIsDeleteDialogOpen(false);
      setSelectedRole(null);
    } catch (error) {
      console.error("Failed to delete role:", error);
    }
  };

  const totalPermissions = roles.reduce((total, role) => total + role.permissions.length, 0);
  const totalRoles = roles.length;

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Roles & Permissions</h1>
          <p className="text-muted-foreground">
            Manage user roles and their permissions across the system
          </p>
        </div>
        <Button onClick={() => setIsCreateDialogOpen(true)} className="gap-2">
          <Plus className="h-4 w-4" />
          Create Role
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Roles</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalRoles}</div>
            <p className="text-xs text-muted-foreground">
              Active roles in system
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Permissions</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalPermissions}</div>
            <p className="text-xs text-muted-foreground">
              Permissions configured
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Resources</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">7</div>
            <p className="text-xs text-muted-foreground">
              Protected resources
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Actions</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">4</div>
            <p className="text-xs text-muted-foreground">
              CRUD operations
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Error Display */}
      {error && (
        <Card className="border-destructive">
          <CardContent className="pt-6">
            <p className="text-destructive">{error}</p>
          </CardContent>
        </Card>
      )}

      {/* Roles Table */}
      <Card>
        <CardHeader>
          <CardTitle>Roles Management</CardTitle>
          <CardDescription>
            Create, edit, and manage user roles and their associated permissions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <RolesTable
            roles={roles}
            loading={loading}
            onEdit={handleEditRole}
            onDelete={handleDeleteRole}
          />
        </CardContent>
      </Card>

      {/* Dialogs */}
      <CreateRoleDialog
        open={isCreateDialogOpen}
        onOpenChange={setIsCreateDialogOpen}
        onSubmit={handleCreateRole}
      />

      <EditRoleDialog
        open={isEditDialogOpen}
        onOpenChange={setIsEditDialogOpen}
        role={selectedRole}
        onSubmit={handleUpdateRole}
      />

      <DeleteRoleDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
        role={selectedRole}
        onConfirm={handleConfirmDelete}
      />
    </div>
  );
}
