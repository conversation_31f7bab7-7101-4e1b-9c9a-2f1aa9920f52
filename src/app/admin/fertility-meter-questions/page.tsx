"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ShadcnUI/tabs";
import { Checkbox } from "@/components/ShadcnUI/checkbox";
import { Label } from "@/components/ShadcnUI/label";

const TABS = {
  BIOLOGICAL: "Biological Factors",
  LIFESTYLE: "Lifestyle & Psychosocial",
  ENVIRONMENTAL: "Environmental & Socioeconomic Factors",
  SETTINGS: "Question Settings",
};

const questions = {
  [TABS.BIOLOGICAL]: [
    "Age of female partner",
    "Regularity of menstrual cycle",
    "History of STIs",
  ],
  [TABS.LIFESTYLE]: [
    "Smoking habits",
    "Alcohol consumption",
    "Stress levels",
  ],
  [TABS.ENVIRONMENTAL]: [
    "Exposure to toxins",
    "Access to healthcare",
    "Socioeconomic status",
  ],
};

export default function FertilityMeterQuestionsPage() {
  const [activeTab, setActiveTab] = useState(TABS.BIOLOGICAL);

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-4">Fertility Meter Questions</h1>
      <Tabs
        value={activeTab}
        onValueChange={setActiveTab}
        orientation="vertical"
        className="flex"
      >
        <TabsList className="flex flex-col h-full">
          <TabsTrigger value={TABS.BIOLOGICAL}>{TABS.BIOLOGICAL}</TabsTrigger>
          <TabsTrigger value={TABS.LIFESTYLE}>{TABS.LIFESTYLE}</TabsTrigger>
          <TabsTrigger value={TABS.ENVIRONMENTAL}>
            {TABS.ENVIRONMENTAL}
          </TabsTrigger>
          <TabsTrigger value={TABS.SETTINGS}>{TABS.SETTINGS}</TabsTrigger>
        </TabsList>

        <div className="flex-grow pl-6">
          <TabsContent value={TABS.BIOLOGICAL}>
            {questions[TABS.BIOLOGICAL].map((q, i) => (
              <QuestionRow key={i} question={q} />
            ))}
          </TabsContent>
          <TabsContent value={TABS.LIFESTYLE}>
            {questions[TABS.LIFESTYLE].map((q, i) => (
              <QuestionRow key={i} question={q} />
            ))}
          </TabsContent>
          <TabsContent value={TABS.ENVIRONMENTAL}>
            {questions[TABS.ENVIRONMENTAL].map((q, i) => (
              <QuestionRow key={i} question={q} />
            ))}
          </TabsContent>
          <TabsContent value={TABS.SETTINGS}>
            <p>Question settings content goes here.</p>
          </TabsContent>
        </div>
      </Tabs>
    </div>
  );
}

function QuestionRow({ question }: { question: string }) {
  return (
    <div className="flex items-center justify-between py-2 border-b">
      <p>{question}</p>
      <div className="flex items-center space-x-4">
        <div className="flex items-center space-x-2">
          <Checkbox id={`${question}-t1`} />
          <Label htmlFor={`${question}-t1`}>T1</Label>
        </div>
        <div className="flex items-center space-x-2">
          <Checkbox id={`${question}-t2`} />
          <Label htmlFor={`${question}-t2`}>T2</Label>
        </div>
        <div className="flex items-center space-x-2">
          <Checkbox id={`${question}-t3`} />
          <Label htmlFor={`${question}-t3`}>T3</Label>
        </div>
      </div>
    </div>
  );
}
