import { useState, useCallback } from "react";
import type { 
  RoleWithPermissions, 
  CreateRoleRequest, 
  UpdateRoleRequest 
} from "@/types/roles-permissions";

interface UseRolesPermissionsReturn {
  roles: RoleWithPermissions[];
  loading: boolean;
  error: string | null;
  createRole: (roleData: CreateRoleRequest) => Promise<void>;
  updateRole: (id: string, roleData: UpdateRoleRequest) => Promise<void>;
  deleteRole: (id: string) => Promise<void>;
  refreshRoles: () => Promise<void>;
}

export function useRolesPermissions(): UseRolesPermissionsReturn {
  const [roles, setRoles] = useState<RoleWithPermissions[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const getAuthHeaders = () => {
    const sessionData = localStorage.getItem("supabase.auth.token");
    if (!sessionData) {
      return { "Content-Type": "application/json" };
    }
    try {
      const session = JSON.parse(sessionData);
      const token = session?.access_token;
      return {
        "Content-Type": "application/json",
        ...(token && { Authorization: `Bearer ${token}` }),
      };
    } catch (error) {
      console.error("Failed to parse auth token from localStorage", error);
      return { "Content-Type": "application/json" };
    }
  };

  const refreshRoles = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch("/api/v1/admin/roles", {
        headers: getAuthHeaders(),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to fetch roles");
      }

      const data = await response.json();
      setRoles(data.roles || []);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Unknown error occurred";
      setError(errorMessage);
      console.error("Error fetching roles:", err);
    } finally {
      setLoading(false);
    }
  }, []);

  const createRole = useCallback(async (roleData: CreateRoleRequest) => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch("/api/v1/admin/roles", {
        method: "POST",
        headers: getAuthHeaders(),
        body: JSON.stringify(roleData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to create role");
      }

      const data = await response.json();
      setRoles(prev => [data.role, ...prev]);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Unknown error occurred";
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const updateRole = useCallback(async (id: string, roleData: UpdateRoleRequest) => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch("/api/v1/admin/roles", {
        method: "PUT",
        headers: getAuthHeaders(),
        body: JSON.stringify({ id, ...roleData }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to update role");
      }

      const data = await response.json();
      setRoles(prev => 
        prev.map(role => 
          role.id.toString() === id ? data.role : role
        )
      );
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Unknown error occurred";
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const deleteRole = useCallback(async (id: string) => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/v1/admin/roles?id=${id}`, {
        method: "DELETE",
        headers: getAuthHeaders(),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to delete role");
      }

      setRoles(prev => prev.filter(role => role.id.toString() !== id));
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Unknown error occurred";
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    roles,
    loading,
    error,
    createRole,
    updateRole,
    deleteRole,
    refreshRoles,
  };
}
