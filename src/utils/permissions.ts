import type { Permission } from "@/types/roles-permissions";

/**
 * Utility functions for checking user permissions
 */

export interface UserPermissions {
  permissions: Permission[];
}

/**
 * Check if user has a specific permission for a resource
 */
export function hasPermission(
  userPermissions: Permission[],
  resource: string,
  action: 'create' | 'read' | 'update' | 'delete'
): boolean {
  const permission = userPermissions.find(p => p.resource === resource);
  
  if (!permission) {
    return false;
  }

  switch (action) {
    case 'create':
      return permission.can_create;
    case 'read':
      return permission.can_read;
    case 'update':
      return permission.can_update;
    case 'delete':
      return permission.can_delete;
    default:
      return false;
  }
}

/**
 * Check if user has any permissions for a resource
 */
export function hasAnyPermission(
  userPermissions: Permission[],
  resource: string
): boolean {
  const permission = userPermissions.find(p => p.resource === resource);
  
  if (!permission) {
    return false;
  }

  return permission.can_create || 
         permission.can_read || 
         permission.can_update || 
         permission.can_delete;
}

/**
 * Check if user has all specified permissions for a resource
 */
export function hasAllPermissions(
  userPermissions: Permission[],
  resource: string,
  actions: ('create' | 'read' | 'update' | 'delete')[]
): boolean {
  return actions.every(action => hasPermission(userPermissions, resource, action));
}

/**
 * Get all resources the user has any permission for
 */
export function getAccessibleResources(userPermissions: Permission[]): string[] {
  return userPermissions
    .filter(p => p.can_create || p.can_read || p.can_update || p.can_delete)
    .map(p => p.resource);
}

/**
 * Get user's permissions for a specific resource
 */
export function getResourcePermissions(
  userPermissions: Permission[],
  resource: string
): {
  canCreate: boolean;
  canRead: boolean;
  canUpdate: boolean;
  canDelete: boolean;
} {
  const permission = userPermissions.find(p => p.resource === resource);
  
  return {
    canCreate: permission?.can_create || false,
    canRead: permission?.can_read || false,
    canUpdate: permission?.can_update || false,
    canDelete: permission?.can_delete || false,
  };
}

/**
 * React hook for permission checking
 */
export function usePermissions(userPermissions: Permission[]) {
  return {
    hasPermission: (resource: string, action: 'create' | 'read' | 'update' | 'delete') =>
      hasPermission(userPermissions, resource, action),
    hasAnyPermission: (resource: string) =>
      hasAnyPermission(userPermissions, resource),
    hasAllPermissions: (resource: string, actions: ('create' | 'read' | 'update' | 'delete')[]) =>
      hasAllPermissions(userPermissions, resource, actions),
    getAccessibleResources: () =>
      getAccessibleResources(userPermissions),
    getResourcePermissions: (resource: string) =>
      getResourcePermissions(userPermissions, resource),
  };
}

/**
 * Higher-order component for permission-based rendering
 */
export function withPermission<T extends object>(
  Component: React.ComponentType<T>,
  requiredPermission: {
    resource: string;
    action: 'create' | 'read' | 'update' | 'delete';
  }
) {
  return function PermissionWrappedComponent(props: T & { userPermissions: Permission[] }) {
    const { userPermissions, ...componentProps } = props;
    
    if (!hasPermission(userPermissions, requiredPermission.resource, requiredPermission.action)) {
      return null;
    }
    
    return <Component {...(componentProps as T)} />;
  };
}

/**
 * Permission-based conditional rendering component
 */
interface PermissionGateProps {
  userPermissions: Permission[];
  resource: string;
  action?: 'create' | 'read' | 'update' | 'delete';
  requireAll?: boolean;
  actions?: ('create' | 'read' | 'update' | 'delete')[];
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

export function PermissionGate({
  userPermissions,
  resource,
  action,
  requireAll = false,
  actions,
  children,
  fallback = null,
}: PermissionGateProps) {
  let hasAccess = false;

  if (action) {
    hasAccess = hasPermission(userPermissions, resource, action);
  } else if (actions) {
    if (requireAll) {
      hasAccess = hasAllPermissions(userPermissions, resource, actions);
    } else {
      hasAccess = actions.some(a => hasPermission(userPermissions, resource, a));
    }
  } else {
    hasAccess = hasAnyPermission(userPermissions, resource);
  }

  return hasAccess ? <>{children}</> : <>{fallback}</>;
}

// Example usage:
/*
// In a component:
import { PermissionGate, usePermissions } from '@/utils/permissions';

function MyComponent({ userPermissions }) {
  const permissions = usePermissions(userPermissions);

  return (
    <div>
      <PermissionGate 
        userPermissions={userPermissions} 
        resource="ivf_scores" 
        action="create"
      >
        <Button>Create IVF Score</Button>
      </PermissionGate>

      {permissions.hasPermission('users', 'read') && (
        <UsersList />
      )}
    </div>
  );
}
*/
